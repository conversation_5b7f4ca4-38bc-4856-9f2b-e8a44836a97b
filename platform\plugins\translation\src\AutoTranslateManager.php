<?php

namespace Botble\Translation;

use Botble\Translation\Exceptions\TranslationRequestException;
use Botble\Translation\Services\GoogleTranslate;
use Illuminate\Support\Str;
use Throwable;

class AutoTranslateManager
{
    public function handle(string $source, string $target, string $value): ?string
    {
        $originalValue = $value;

        $value = str_replace('%', '#_#', $value);

        // Handle Laravel-style variables with colon prefix
        $colonVariables = $this->findVariablesByRule($value, ':');
        foreach ($colonVariables as $item) {
            $value = str_replace($item, '#_COLON_VAR_#', $value);
        }

        $variables = $this->findVariablesByRule($value, '%s');

        foreach ($variables as $item) {
            $value = str_replace($item, '%s', $value);
        }

        try {
            $translated = (new GoogleTranslate())
                ->setSource($source)
                ->setTarget($target)
                ->translate($value);
        } catch (TranslationRequestException) {
            return $originalValue;
        }

        $translated = str_replace('%S', '%s', $translated);

        if (count($this->findVariablesByRule($translated, '%s')) !== count($variables)) {
            return $originalValue;
        }

        try {
            $translated = sprintf($translated, ...$variables);
        } catch (Throwable) {
            return $originalValue;
        }

        $translated = str_replace('#_#', '%', $translated);
        $translated = str_replace('#_ #', '%', $translated);

        // Restore Laravel-style variables
        for ($i = 0; $i < count($colonVariables); $i++) {
            $translated = preg_replace('/#_COLON_VAR_#/', $colonVariables[$i], $translated, 1);
        }

        // Check if all variables were properly restored
        if (count($colonVariables) > 0 && !str_contains($translated, ':')) {
            return $originalValue;
        }

        return $translated;
    }

    public function translate(string $source, string $target, string $value): ?string
    {
        $translated = app(Dictionary::class)->locale($target)->getTranslate($value);

        if ($translated) {
            return $translated;
        }

        return $this->handle($source, $target, $value);
    }

    protected function findVariablesByRule(string $text, string $rule): array
    {
        return array_values(array_filter(explode(' ', $text), function ($item) use ($rule) {
            return str_replace($rule, '', $item) && Str::startsWith($item, $rule);
        }));
    }
}

