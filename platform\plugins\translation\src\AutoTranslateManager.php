<?php

namespace Botble\Translation;

use Bo<PERSON>ble\Translation\Exceptions\TranslationRequestException;
use Botble\Translation\Services\GoogleTranslate;
use Illuminate\Support\Str;
use Throwable;

class AutoTranslateManager
{
    public function handle(string $source, string $target, string $value): ?string
    {
        $originalValue = $value;

        $value = str_replace('%', '#_#', $value);

        // Handle Laravel-style variables with colon prefix
        $colonVariables = $this->findVariablesByRule($value, ':');
        foreach ($colonVariables as $index => $item) {
            $value = str_replace($item, '#_COLON_VAR_' . $index . '_#', $value);
        }

        $variables = $this->findVariablesByRule($value, '%s');

        foreach ($variables as $item) {
            $value = str_replace($item, '%s', $value);
        }

        try {
            $translated = (new GoogleTranslate())
                ->setSource($source)
                ->setTarget($target)
                ->translate($value);
        } catch (TranslationRequestException) {
            return $originalValue;
        }

        $translated = str_replace('%S', '%s', $translated);

        if (count($this->findVariablesByRule($translated, '%s')) !== count($variables)) {
            return $originalValue;
        }

        try {
            $translated = sprintf($translated, ...$variables);
        } catch (Throwable) {
            return $originalValue;
        }

        $translated = str_replace('#_#', '%', $translated);
        $translated = str_replace('#_ #', '%', $translated);

        // Restore Laravel-style variables with comprehensive pattern matching
        foreach ($colonVariables as $index => $variable) {
            // Create a flexible regex pattern that handles all variations Google Translate might introduce
            // This pattern matches: #[optional spaces]_[optional spaces][colon_var in any case][optional spaces]_[optional spaces][index][optional spaces]_[optional spaces]#
            $flexiblePattern = '/#+\s*_?\s*(?:colon_var|COLON_VAR|Colon_var|colon_VAR)\s*_?\s*' . $index . '\s*_?\s*#+/i';
            $translated = preg_replace($flexiblePattern, $variable, $translated);
        }

        // Check if all variables were properly restored
        if (count($colonVariables) > 0 && !str_contains($translated, ':')) {
            return $originalValue;
        }

        return $translated;
    }

    public function translate(string $source, string $target, string $value): ?string
    {
        $translated = app(Dictionary::class)->locale($target)->getTranslate($value);

        if ($translated) {
            return $translated;
        }

        return $this->handle($source, $target, $value);
    }

    protected function findVariablesByRule(string $text, string $rule): array
    {
        // Handle Laravel-style variables with colon prefix (e.g., :name, :count)
        if ($rule === ':') {
            preg_match_all('/:[a-zA-Z_][a-zA-Z0-9_]*/', $text, $matches);
            return array_values(array_unique($matches[0]));
        }

        // Handle sprintf-style variables (e.g., %s)
        if ($rule === '%s') {
            preg_match_all('/%s/', $text, $matches);
            return array_values($matches[0]);
        }

        // Fallback to original logic for other rules
        return array_values(array_filter(explode(' ', $text), function ($item) use ($rule) {
            return str_replace($rule, '', $item) && Str::startsWith($item, $rule);
        }));
    }
}

